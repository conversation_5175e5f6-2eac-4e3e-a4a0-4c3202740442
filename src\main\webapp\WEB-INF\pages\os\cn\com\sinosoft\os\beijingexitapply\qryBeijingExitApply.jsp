<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<jsp:directive.page import="ie.weaf.toolkit.Util" />
<%
	String title = "出京申请查询"; // 标题
%>
<html>
<head>
	<title><%=title%></title>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<%@ include file="/common/taglibs.jsp"%>
	<style type="text/css">
	.myrow {
		 color: gray;
	}
	</style>
	<script type="text/javascript">
		var grid,form,perm = false;
		// ?????????
		$(document).ready(function() {
			mini.parse();

			var n = mini.get("layout1").getRegion("north");
			if(n){
				n.height=$("#qryTable").height()+35;
				mini.layout();
				form = new mini.Form("form1");
			}
			grid = mini.get("datagrid1");
			if (perm) {
				// ???Grid????в?
				grid.on("rowdblclick", function(e) {
					view();
				});
			}
			grid.on("drawcell", function (e) {
				var record = e.record;
				//???????
				if (record.STATE_CODE == '0') {
					e.rowCls = "myrow";
				}
			});
			search();

			// ??????????
			try {
				if (window.parent && window.parent.mini) {
					// ?????mini?????д????????
					var win = window.parent.mini.getWindow(window);
					if (win && win.max) {
						win.max();
					}
				} else {
					// ???????????????????????????С
					if (window.screen && window.screen.availWidth && window.screen.availHeight) {
						window.resizeTo(window.screen.availWidth, window.screen.availHeight);
						window.moveTo(0, 0);
					}
				}
			} catch (e) {
				// ??????????????
				console.log("??????????????" + e.message);
			}
		});


		// ???????
		function search() {
			if(form){
				form.validate();
				if (form.isValid() == false){
					showFormErrorTexts(form.getErrorTexts(),400,300);
					return;
				}
			}

			grid.load({
				session_APPLY_NAME : encodeURIComponent(mini.get("session_APPLY_NAME").getValue()), // ??????????
				session_IDENTITY_TYPE : encodeURIComponent(mini.get("session_IDENTITY_TYPE").getValue()), // ???????
				session_APPLY_DATE_BEGIN : encodeURIComponent(mini.get("session_APPLY_DATE_BEGIN").getFormValue()), // ??????????
				session_APPLY_DATE_END : encodeURIComponent(mini.get("session_APPLY_DATE_END").getFormValue()) // ???????????
			},function(){
				//??????????????????
			});
		}

		// ??
		function view() {
			var records = grid.getSelecteds();
			if (records.length > 1) {
				mini.alert('??????????????');
				return;
			}
			var record = records[0];
			if (record) {
				var win = mini.open({
					url : "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_viewParent.ac?pageState=view&id="
						+ record.ID,
					title : "??",
					width : 800,
					height : 400,
					showMaxButton : true,
					ondestroy : function(action) {
						// ??????????
					}
				});
			 win.max();
			} else {
				mini.alert("???????????");
			}
		}

		// ????
		function add() {
			var win = mini.open({
				url : "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_addParentInput.ac?pageState=add",
				title : "????",
				width : 800,
				height : 400,
				showMaxButton : true,
				ondestroy : function(action) {
					// ??????????
					if (action == "save"){
						grid.reload();
					}
				}
			});
			 win.max();
		}

		// ???
		function edit() {
			var records = grid.getSelecteds();
			if (records.length > 1) {
				mini.alert('???????????????');
				return;
			}
			var record = records[0];
			if (record) {
				// ????????
				// ??в???????????
				//????????
				var win = mini.open({
					url : "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_edtParentInput.ac?pageState=edit&id="
						+ record.ID,
					title : "???",
					width : 800,
					height : 400,
					showMaxButton : true,
					ondestroy : function(action) {
						// ??????????
						if (action == "save"){
							grid.reload();
						}
					}
				});
				 win.max();
			} else {
				mini.alert("???????????");
			}
		}

		// ???
		function remove() {
			var rows = grid.getSelecteds();
			if (rows.length > 0) {
				// ????????
				// ??в???????????
				// ??????
				mini.confirm ("?????????е?" + rows.length + "???????","??????",
					function(action) {
						if (action == "ok") {
							var id = [];
							for (var i = 0, l = rows.length; i < l; i++) {
								id.push(rows[i].ID);
							}
							id = id.join(',');
							//??????
							grid.loading("????У??????......");
							$.ajax({
								url : "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_delParentSubmit.ac?id="
									+ id,
								success : function() {
									grid.reload();
								},
								error : function(XMLHttpRequest, textStatus, errorThrown) {

								}
							});
						}
					}
				);
			} else {
				mini.alert("?????????????");
			}
		}

		// ????????
		function onAuditStateRenderer(e) {
			var record = e.record;
			var value = e.value;
			if (value == "0") {
				return '<span style="color: orange;">?????</span>';
			} else if (value == "1") {
				return '<span style="color: green;">?????</span>';
			} else if (value == "2") {
				return '<span style="color: red;">?????</span>';
			} else {
				return '<span style="color: gray;">δ?</span>';
			}
		}
	</script>
</head>
<body>
	<!-- ????Excel???HTML start -->
	<form id="excelForm" enctype="multipart/form-data"
		action="${ctx }/util/util_exportExcle.ac" method="post"
		target="excelIFrame" style="display:none;">
		<!-- ????????? -->
		<input type="hidden" name="headData" id="headData" />
		<!-- ?????б?????????????????????? -->
		<input type="hidden" name="bodyData" id="bodyData" />
		<!-- ??????????????????field???????? -->
		<input type="hidden" name="export_hideColumn" id="export_hideColumn" value="" />
		<!-- ?????????????field???????? -->
		<input type="hidden" name="export_showColumn" id="export_showColumn" value=""/>
	</form>
	<iframe id="excelIFrame" name="excelIFrame" style="display:none;"></iframe>
	<!-- ????Excel???HTML end -->
	<div id="layout1" class="mini-layout" style="width: 100%; height: 100%;">
		<div title="<%=title%>" region="north" height="120" showHeader="true" class="util_search" >
			<div id="form1" style="padding: 0;margin: 0;">
				<table id="qryTable" align="center" style="margin-top: 6px;" cellpadding="4">
					<tr>
						<td align="right"><nobr>&nbsp;???????</nobr></td>
						<td>
							<bspHtml:TextBox property="session_APPLY_NAME"
								onenter="search()" vtype="maxLength:50;rangeChar:0,50;" maxLength="50"
								maxLengthErrorText="[??????] ??????? {0} ?????"
								rangeCharErrorText="[??????] ???????????? {0} ?? {1} ???" style="width:120px;">
							</bspHtml:TextBox>
						</td>
						<td align="right"><nobr>&nbsp;&nbsp;????????</nobr></td>
						<td>
							<bspHtml:ComboBox property="session_IDENTITY_TYPE"
								url="IDENTITY_TYPE" style="width:120px;"
								onenter="search()">
							</bspHtml:ComboBox>
						</td>
						<td align="right"><nobr>&nbsp;&nbsp;?????????</nobr></td>
						<td>
							<bspHtml:DatePicker property="session_APPLY_DATE_BEGIN"
								format="yyyy-MM-dd"
								value="<%=Util.getMonthFirstDay()%>" style="width:120px;">
							</bspHtml:DatePicker>
						</td>
						<td align="center"><nobr>&nbsp;??&nbsp;</nobr></td>
						<td>
							<bspHtml:DatePicker property="session_APPLY_DATE_END"
								format="yyyy-MM-dd"
								style="width:120px;">
							</bspHtml:DatePicker>
						</td>
						<td align="center">
							<a class="mini-button" iconCls="icon-search" onclick="search()">???</a>
						</td>
					</tr>
				</table>
			</div>
		</div>
		<div title="<%=title%>" region="center" showHeader="false">
			<div class="mini-toolbar" style="padding: 2px;" borderStyle="border-left:0;border-top:0;border-right:0;">
					<script>perm = true;</script>
					<a class="mini-button" iconCls="icon-node" plain="true" onclick="view()">??</a>
					<a class="mini-button" iconCls="icon-add" plain="true" onclick="add()">????</a>
					<a class="mini-button" iconCls="icon-edit" plain="true" onclick="edit()">???</a>
					<a class="mini-button" iconCls="icon-remove" plain="true" onclick="remove()">???</a>
					<a class="mini-button" iconCls="icon-download" plain="true" onclick="exportExcel(grid)">????Excel</a>
			</div>
			<!--?????б?-->
			<div class="mini-fit">
				<div id="datagrid1" class="mini-datagrid" idField="ID" sortMode="client"
					 allowAlternating="true" url="${ctx }/cn/com/sinosoft/os/beijingexitapply/qryBeijingExitApplyList.ac"
					 style="width: 100%; height: 100%;" sizeList="[20,50,100]" pageSize="20"
					 multiSelect="true" borderStyle="border:0" selectOnLoad="true">
					<div property="columns">
						<div type="checkcolumn" align="center" headerAlign="center" width="50"></div>
						<div type="indexcolumn" align="center" headerAlign="center" width="50">???</div>

						<div field="APPLY_NAME" width="100" headerAlign="center" align="center">??????????</div>
						<div field="DEPARTMENT" width="120" headerAlign="center" align="center">????</div>
						<div field="APPLY_DATE" width="100" headerAlign="center" align="center" dateFormat="yyyy-MM-dd">????????</div>
						<div field="START_DATE" width="100" headerAlign="center" align="center" dateFormat="yyyy-MM-dd">??????</div>
						<div field="END_DATE" width="100" headerAlign="center" align="center" dateFormat="yyyy-MM-dd">???????</div>
						<div field="TRAVEL_DAYS" width="80" headerAlign="center" align="center">????????</div>
						<div field="APPLY_DESTN" width="120" headerAlign="center" align="center">????</div>
						<div field="TRAVEL_REASON" width="200" headerAlign="center" align="left">???????</div>
						<div field="AUDIT_STATE" width="100" headerAlign="center" align="center" renderer="onAuditStateRenderer">?????</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>