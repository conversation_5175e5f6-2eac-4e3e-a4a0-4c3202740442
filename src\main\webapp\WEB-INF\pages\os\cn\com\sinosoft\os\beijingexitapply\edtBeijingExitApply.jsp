﻿<%@ page language="java" contentType="text/html; charset=GBK" pageEncoding="GBK"%>
<%@ page import="ie.weaf.toolkit.Util"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
	<title>鍑轰含鐢宠绠＄悊 - 缂栬緫</title>
	<meta http-equiv="content-type" content="text/html; charset=GBK" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<%@ include file="/common/taglibs.jsp"%>
	<style type="text/css">
		.form-container {
			width: 80%;
			margin: 20px auto;
			padding: 20px;
			border: 1px solid #ddd;
			border-radius: 5px;
			background-color: #f9f9f9;
		}
		.form-title {
			text-align: center;
			font-size: 18px;
			font-weight: bold;
			margin-bottom: 20px;
			color: #333;
		}
		.audit-section {
			margin-top: 20px;
			padding: 15px;
			border: 1px solid #ccc;
			border-radius: 5px;
			background-color: #fff;
		}
		.audit-title {
			font-weight: bold;
			color: #666;
			margin-bottom: 10px;
		}
	</style>
	<script type="text/javascript">
		var form;
		$(document).ready(function() {
			mini.parse();
			form = new mini.Form("form1");

			// ????????????????
			var pageState = "${pageState}";
			if (pageState == "view") {
				// ???????????
				form.setEnabled(false);
			}

			// ?????????
			try {
				if (window.parent && window.parent.mini) {
					// ?????mini?????写????????
					var win = window.parent.mini.getWindow(window);
					if (win && win.max) {
						win.max();
					}
				} else {
					// ??????????????????????????小
					if (window.screen && window.screen.availWidth && window.screen.availHeight) {
						window.resizeTo(window.screen.availWidth, window.screen.availHeight);
						window.moveTo(0, 0);
					}
				}
			} catch (e) {
				// ???????????
				console.log("?????????????" + e.message);
			}
		});

		//????
		function save(e) {
			form.validate();
			if (form.isValid() == false) {
				//??????????????????400???300
				showFormErrorTexts(form.getErrorTexts(),400,300);
				form.getErrors()[0].focus();
				return;
			}

			if ("${pageState}" == "add") {
				document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_addParentSubmit.ac";
			} else {
				document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_edtParentSubmit.ac";
			}
			document.form1.submit();
			waitClick();
		}

		//鎻愪氦娴佺▼
		function submit() {
			form.validate();
			if (form.isValid() == false) {
				//??????????????????400???300
				showFormErrorTexts(form.getErrorTexts(),400,300);
				form.getErrors()[0].focus();
				return;
			}

			mini.confirm("??????????????????????????????????", "???", function (action) {
				if (action == "ok") {
					// ????type????????2???????????????
					var typeInput = document.createElement("input");
					typeInput.type = "hidden";
					typeInput.name = "type";
					typeInput.value = "2";
					document.form1.appendChild(typeInput);

					if ("${pageState}" == "add") {
						document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_addParentSubmit.ac";
					} else {
						document.form1.action = "${ctx}/cn/com/sinosoft/os/beijingexitapply/beijingExitApply_edtParentSubmit.ac";
					}
					document.form1.submit();
					waitClick();
				}
			});
		}

		//瀹℃牳鎻愪氦
		function auditSubmit(auditResult) {
			var auditRemark = mini.get("auditRemark").getValue();
			if (!auditRemark) {
				mini.alert("????写??????");
				return;
			}

			document.form1.action = "${ctx}/auditBeijingExitApplySubmit.ac";
			document.getElementById("auditResult").value = auditResult;
			document.form1.submit();
		}
	</script>
</head>
<body>
	<form id="form1" name="form1" method="post">
		<div style="display:none;">
			<input type="hidden" name="id" value="${result.id}" />
			<input type="hidden" name="pageState" value="${pageState}" />
			<input type="hidden" id="auditResult" name="auditResult" value="" />
			<input type="hidden" name="taskid" value="${taskid}" />
			<input type="hidden" name="parama" value="${parama}" />
			<input type="hidden" name="piId" value="${piId}" />
		</div>

	<div class="form-container">
		<div class="form-title">?????????</div>
		<table class="tab-1" cellpadding="8" cellspacing="0" border="1" style="width:100%; border-collapse: collapse;">
		<COLGROUP>
			<col align="right" style="width:20%"/>
			<col align="left" style="width:30%"/>
			<col align="right" style="width:20%"/>
			<col align="left" style="width:30%"/>
		</COLGROUP>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;??????????
			</td>
			<td>
				<bspHtml:TextBox property="result.applyName"
					style="width:100%;"
					emptyText="????????????????" maxLength="50" required="true"
					value="${sessionScope.SESSION_USERVIEW.name}"
					vtype="rangeChar:1,50;"
					rangeCharErrorText="[??????????] ???????????? {0} ?? {1} ???">
				</bspHtml:TextBox>
			</td>
			<td align="right" class="bgcolor">
				????
			</td>
			<td>
				<bspHtml:TextBox property="result.department"
					style="width:100%;"
					emptyText="????????" maxLength="100" enabled="false"
					value="${sessionScope.SESSION_USERVIEW.departmentName}"
					vtype="rangeChar:0,100;"
					rangeCharErrorText="[????] ???????????? {0} ?? {1} ???">
				</bspHtml:TextBox>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;????????
			</td>
			<td>
				<bspHtml:DatePicker property="result.applyDate"
					style="width:100%;" required="true" enabled="false"
					format="yyyy-MM-dd" value="<%=Util.getToDay()%>" emptyText="?????????????">
				</bspHtml:DatePicker>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;???????
			</td>
			<td>
				<bspHtml:RadioButtonList property="result.identityType"
					url="IDENTITY_TYPE" style="width:100%;" required="true"
					textField="NAME" valueField="ID">
				</bspHtml:RadioButtonList>
			</td>
		</tr>
		<c:if test="${sessionScope.SESSION_USERVIEW.position ne '10'}">
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;鐩存帴涓婄骇棰嗗
			</td>
			<td colspan="3">
				<bspHtml:ComboBox property="result.leader"
					style="width:100%;"
					url="CM_ZGSLDSP_USERNAME"
					allowInput="false"
					viewState="edit"
					emptyText="璇烽�夋嫨鐩存帴涓婄骇棰嗗锛屽鏃犺閫夋嫨"
					textField="NAME" valueField="ID"
					requiredErrorText="[棰嗗] 蹇呴』閫夋嫨">
				</bspHtml:ComboBox>
			</td>
		</tr>
		</c:if>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;寮�濮嬫棩鏈�
			</td>
			<td>
				<bspHtml:DatePicker property="result.startDate"
					style="width:100%;" required="true"
					format="yyyy-MM-dd" emptyText="璇烽�夋嫨寮�濮嬫棩鏈�">
				</bspHtml:DatePicker>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;缁撴潫鏃ユ湡
			</td>
			<td>
				<bspHtml:DatePicker property="result.endDate"
					style="width:100%;" required="true"
					format="yyyy-MM-dd" emptyText="璇烽�夋嫨缁撴潫鏃ユ湡">
				</bspHtml:DatePicker>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				鍑鸿澶╂暟
			</td>
			<td>
				<bspHtml:TextBox property="result.travelDays"
					style="width:100%;"
					emptyText="璇疯緭鍏ュ嚭琛屽ぉ鏁�" maxLength="10"
					vtype="int" intErrorText="璇疯緭鍏ユ纭殑鏁板瓧">
				</bspHtml:TextBox>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;鐩殑鍦�
			</td>
			<td>
				<bspHtml:TextBox property="result.applyDestn"
					style="width:100%;" required="true"
					emptyText="璇疯緭鍏ョ洰鐨勫湴" maxLength="100"
					vtype="rangeChar:1,100;"
					rangeCharErrorText="[鐩殑鍦癩 瀛楃闀垮害蹇呴』鍦� {0} 鍒� {1} 涔嬮棿">
				</bspHtml:TextBox>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;鍑鸿鍘熷洜
			</td>
			<td colspan="3">
				<bspHtml:TextArea property="result.travelReason"
					style="width:100%;height:80px;" required="true"
					emptyText="璇疯緭鍏ュ嚭琛屽師鍥�" maxLength="500"
					vtype="rangeChar:1,500;"
					rangeCharErrorText="[鍑鸿鍘熷洜] 瀛楃闀垮害蹇呴』鍦� {0} 鍒� {1} 涔嬮棿">
				</bspHtml:TextArea>
			</td>
		</tr>
		</table>

		<!-- 瀹℃牳鎰忚鍖哄煙 -->
		<c:if test="${pageState eq 'view' and not empty taskid}">
		<div class="audit-section">
			<div class="audit-title">瀹℃牳鎰忚</div>
			<table class="tab-1" cellpadding="8" cellspacing="0" border="1" style="width:100%; border-collapse: collapse;">
				<COLGROUP>
					<col align="right" style="width:20%"/>
					<col align="left" style="width:80%"/>
				</COLGROUP>
				<tr>
					<td align="right" class="bgcolor">
						<font color="red">*</font>&nbsp;瀹℃牳鎰忚
					</td>
					<td>
						<bspHtml:TextArea property="auditRemark"
							style="width:100%;height:80px;" required="true"
							emptyText="璇疯緭鍏ュ鏍告剰瑙�" maxLength="500"
							vtype="rangeChar:1,500;"
							rangeCharErrorText="[瀹℃牳鎰忚] 瀛楃闀垮害蹇呴』鍦� {0} 鍒� {1} 涔嬮棿">
						</bspHtml:TextArea>
					</td>
				</tr>
			</table>
		</div>
		</c:if>

		<!-- 鎸夐挳鎿嶄綔鍖哄煙 -->
		<div style="text-align: center; margin-top: 20px;">
			<c:if test="${pageState eq 'add' or pageState eq 'edit'}">
				<a class="mini-button" iconCls="icon-save" onclick="save()">淇濆瓨</a>
				<span style="display:inline-block;width:25px;"></span>
				<a class="mini-button" iconCls="icon-ok" onclick="submit()" style="background-color: #2196F3; color: white;">??</a>
				<span style="display:inline-block;width:25px;"></span>
				<a class="mini-button" iconCls="icon-cancel" onclick="CloseWindow('cancel')">鍙栨秷</a>
			</c:if>

			<c:if test="${pageState eq 'view' and not empty taskid}">
				<a class="mini-button" iconCls="icon-ok" onclick="auditSubmit('1')" style="background-color: #4CAF50; color: white;">瀹℃牳閫氳繃</a>
				<span style="display:inline-block;width:25px;"></span>
				<a class="mini-button" iconCls="icon-cancel" onclick="auditSubmit('dz')" style="background-color: #f44336; color: white;">椹冲洖</a>
				<span style="display:inline-block;width:25px;"></span>
				<a class="mini-button" iconCls="icon-cancel" onclick="CloseWindow('cancel')">鍙栨秷</a>
			</c:if>

			<c:if test="${pageState eq 'view' and empty taskid}">
				<a class="mini-button" iconCls="icon-cancel" onclick="CloseWindow('cancel')">鍏抽棴</a>
			</c:if>
		</div>
	</div>
	</form>
</body>
</html>

